/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 */
const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	mobilnummer: "phone",

	// Email field variations (for future use)
	"e-mail": "email",
	"email address": "email",
	"e-mail address": "email",
	"e-mail-adresse": "email",
	"email-adresse": "email",
	"electronic mail": "email",
};

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}



/**
 * Generate AP fieldKey from CC field name
 *
 * Normalizes the field name to match AutoPatient's auto-generation pattern.
 * This function attempts to replicate how AP generates fieldKeys from field names.
 *
 * @param ccFieldName - CC field name to convert
 * @returns Normalized fieldKey that should match AP's generation
 */
function generateApFieldKey(ccFieldName: string): string {
	// Step-by-step normalization with intermediate logging for debugging
	const step1 = ccFieldName.toLowerCase();
	const step2 = step1.normalize("NFD"); // Decompose Unicode characters
	const step3 = step2.replace(/[\u0300-\u036f]/g, ""); // Remove diacritics (ä -> a, ü -> u, etc.)
	const step4 = step3.replace(/[^a-z0-9]/g, ""); // Remove all non-alphanumeric characters
	const step5 = step4.trim();

	// For debugging complex field names, we can enable detailed logging
	// This is commented out for performance but can be enabled for troubleshooting
	/*
	if (ccFieldName.includes("Befunde") || ccFieldName.length > 10) {
		console.log(`FieldKey generation for "${ccFieldName}":`, {
			step1_lowercase: step1,
			step2_normalized: step2,
			step3_no_diacritics: step3,
			step4_alphanumeric_only: step4,
			step5_final: step5
		});
	}
	*/

	return step5;
}







/**
 * Validate that AutoPatient API response contains no duplicate fieldKeys
 *
 * According to API specifications, each fieldKey should be unique.
 * Duplicates indicate critical data integrity issues requiring investigation.
 *
 * @param apCustomFields - Array of AP custom fields to validate
 * @param requestId - Request ID for logging
 * @throws Error if duplicate fieldKeys are detected
 */
function validateNoDuplicateFieldKeys(
	apCustomFields: APGetCustomFieldType[],
	requestId: string
): void {
	const fieldKeyMap = new Map<string, APGetCustomFieldType[]>();

	apCustomFields.forEach(field => {
		if (field.fieldKey) {
			const key = field.fieldKey.toLowerCase();
			if (!fieldKeyMap.has(key)) {
				fieldKeyMap.set(key, []);
			}
			fieldKeyMap.get(key)!.push(field);
		}
	});

	const duplicates = Array.from(fieldKeyMap.entries()).filter(([_, fields]) => fields.length > 1);

	if (duplicates.length > 0) {
		logError(requestId, `CRITICAL API BUG: AutoPatient API returned ${duplicates.length} duplicate fieldKeys`);
		duplicates.forEach(([key, fields]) => {
			logError(requestId, `Duplicate fieldKey "${key}" in fields: ${fields.map(f => `"${f.name}" (ID: ${f.id})`).join(", ")}`);
		});
		throw new Error(`AutoPatient API data corruption: ${duplicates.length} duplicate fieldKeys detected. This requires immediate investigation.`);
	}
}



/**
 * Find existing AP custom field using exact matching
 *
 * Follows v3Integration pattern for simple exact matching:
 * 1. Direct name matching (CC name vs AP name)
 * 2. Direct label matching (CC label vs AP name)
 * 3. Exact fieldKey matching with basic variations
 * 4. Case-insensitive exact matching as fallback
 *
 * @param apCustomFields - Array of existing AP custom fields
 * @param ccField - CC custom field to find match for
 * @param requestId - Request ID for detailed logging
 * @returns Matching AP field or undefined
 */
function findExistingApField(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): APGetCustomFieldType | undefined {
	const expectedFieldKey = generateApFieldKey(ccField.name);

	logDebug(requestId, `Searching for exact match for CC field "${ccField.name}" (label: "${ccField.label}")`);

	// Try exact name matching first (v3Integration pattern: cf.name === name)
	let match = apCustomFields.find(apField => apField.name === ccField.name);
	if (match) {
		logDebug(requestId, `Found exact name match: "${match.name}" (ID: ${match.id})`);
		return match;
	}

	// Try exact label matching (v3Integration pattern: cf.name === label)
	match = apCustomFields.find(apField => apField.name === ccField.label);
	if (match) {
		logDebug(requestId, `Found exact label match: "${match.name}" (ID: ${match.id})`);
		return match;
	}

	// Try exact fieldKey matching with basic variations
	match = apCustomFields.find(apField =>
		apField.fieldKey === expectedFieldKey ||
		apField.fieldKey === `contact.${expectedFieldKey}` ||
		apField.fieldKey?.replace("contact.", "") === expectedFieldKey
	);
	if (match) {
		logDebug(requestId, `Found exact fieldKey match: "${match.name}" (fieldKey: "${match.fieldKey}", ID: ${match.id})`);
		return match;
	}

	// Case-insensitive exact matching as final fallback
	match = apCustomFields.find(apField =>
		apField.name.toLowerCase() === ccField.name.toLowerCase() ||
		apField.name.toLowerCase() === ccField.label.toLowerCase()
	);
	if (match) {
		logDebug(requestId, `Found case-insensitive exact match: "${match.name}" (ID: ${match.id})`);
		return match;
	}

	logDebug(requestId, `No exact match found for CC field "${ccField.name}"`);
	return undefined;
}

/**
 * Enhanced field existence check with API query fallback
 * First checks local cache, then queries AP API if needed
 */
async function findExistingApFieldWithApiCheck(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | undefined> {
	// First check local cache
	let existingField = findExistingApField(apCustomFields, ccField, requestId);

	if (existingField) {
		logDebug(
			requestId,
			`Found existing field in cache: "${existingField.name}" (ID: ${existingField.id})`,
		);
		return existingField;
	}

	// If not found in cache, refresh from API to ensure we have latest data
	logDebug(
		requestId,
		`Field not found in cache, refreshing AP custom fields from API`,
	);

	try {
		const refreshedFields = await apCustomfield.all();
		logDebug(
			requestId,
			`Refreshed AP custom fields from API, total: ${refreshedFields.length}`,
		);
		existingField = findExistingApField(refreshedFields, ccField, requestId);

		if (existingField) {
			logDebug(
				requestId,
				`Found existing field after API refresh: "${existingField.name}" (ID: ${existingField.id})`,
			);
			// Update the cache with fresh data
			apCustomFields.length = 0;
			apCustomFields.push(...refreshedFields);
		}

		return existingField;
	} catch (error) {
		logError(requestId, `Failed to refresh AP custom fields:`, error);
		return undefined;
	}
}

/**
 * Extract fieldKey from AutoPatient "already exists" error message
 *
 * Parses error messages like "contact.befunde already exists" to extract the fieldKey
 *
 * @param errorMessage - Error message from AutoPatient API
 * @returns Extracted fieldKey or null if not found
 */
function extractFieldKeyFromError(errorMessage: string): string | null {
	// Pattern to match "contact.{fieldKey} already exists" or similar
	const patterns = [
		/contact\.([a-zA-Z0-9_-]+)\s+already\s+exists/i,
		/field\s+['""]?contact\.([a-zA-Z0-9_-]+)['""]?\s+already\s+exists/i,
		/([a-zA-Z0-9_-]+)\s+already\s+exists/i, // Fallback pattern
	];

	for (const pattern of patterns) {
		const match = errorMessage.match(pattern);
		if (match && match[1]) {
			return match[1];
		}
	}

	return null;
}

/**
 * Find existing field by extracted fieldKey from error message
 *
 * Uses the fieldKey extracted from the error message to find the existing field
 *
 * @param apCustomFields - Array of AP custom fields
 * @param extractedFieldKey - FieldKey extracted from error message
 * @param requestId - Request ID for logging
 * @returns Matching field or undefined
 */
function findFieldByExtractedKey(
	apCustomFields: APGetCustomFieldType[],
	extractedFieldKey: string,
	requestId: string,
): APGetCustomFieldType | undefined {
	logDebug(requestId, `Searching for field with extracted fieldKey: "${extractedFieldKey}"`);

	const field = apCustomFields.find((apField) => {
		// Try multiple variations of the extracted fieldKey
		const fieldKeyMatches = [
			apField.fieldKey === extractedFieldKey,
			apField.fieldKey === `contact.${extractedFieldKey}`,
			apField.fieldKey?.replace("contact.", "") === extractedFieldKey,
			apField.fieldKey?.toLowerCase() === extractedFieldKey.toLowerCase(),
			apField.fieldKey?.toLowerCase() === `contact.${extractedFieldKey}`.toLowerCase(),
		];

		return fieldKeyMatches.some(match => match);
	});

	if (field) {
		logDebug(
			requestId,
			`Found field by extracted fieldKey: "${field.name}" (ID: ${field.id}, fieldKey: "${field.fieldKey}")`
		);
	} else {
		logDebug(requestId, `No field found with extracted fieldKey: "${extractedFieldKey}"`);
	}

	return field;
}

/**
 * Check if a field name represents a standard contact field
 * Uses exact matching following v3Integration pattern
 */
function isStandardContactField(fieldName: string): boolean {
	return STANDARD_CONTACT_FIELDS.some(
		(standardField) => standardField === fieldName || standardField.toLowerCase() === fieldName.toLowerCase(),
	);
}

/**
 * Transform boolean field value to Yes/No format for AutoPatient
 *
 * Converts various boolean representations from CliniCore to standardized
 * Yes/No format expected by AutoPatient RADIO fields.
 *
 * @param value - The field value to transform
 * @param fieldType - The CC field type to determine if transformation is needed
 * @returns Transformed value ("Yes"/"No" for boolean fields, original value otherwise)
 *
 * @example
 * ```typescript
* transformBooleanValue("true", "boolean")   // Returns "Yes"
 * transformBooleanValue("false", "boolean")  // Returns "No"
 * transformBooleanValue("1", "boolean")      // Returns "Yes"
 * transformBooleanValue("0", "boolean")      // Returns "No"
 * transformBooleanValue("text", "string")    // Returns "text" (unchanged)
 *
```
 */
function transformBooleanValue(value: string, fieldType: string): string {
	const normalizedType = fieldType.toLowerCase().trim();

	if (normalizedType === "boolean") {
		const normalizedValue = value.toLowerCase().trim();

		// Handle various boolean representations
		if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
			return "Yes";
		} else if (normalizedValue === "false" || normalizedValue === "0" || normalizedValue === "no") {
			return "No";
		}

		// Default to "No" for any other value
		return "No";
	}

	// Return original value for non-boolean fields
	return value;
}

/**
 * Map CC custom field type to AP data type with enhanced logic
 *
 * Provides intelligent mapping between CliniCore and AutoPatient field types,
 * with special handling for boolean fields (mapped to RADIO with Yes/No options)
 * and select fields with multiple values.
 *
 * @param ccField - CliniCore custom field definition containing type and properties
 * @returns AutoPatient data type string (e.g., "RADIO", "MULTIPLE_OPTIONS", "TEXT")
 *
 * @example
 * ```typescript
* // Boolean field mapping
 * mapCcToApDataType({type: "boolean", ...}) // Returns "RADIO"
 *
 * // Multi-select field mapping
 * mapCcToApDataType({type: "select", allowMultipleValues: true, ...}) // Returns "MULTIPLE_OPTIONS"
 *
 * // Standard text field mapping
 * mapCcToApDataType({type: "text", ...}) // Returns "TEXT"
 *
```
 */
function mapCcToApDataType(ccField: GetCCCustomField): string {
	const normalizedType = ccField.type.toLowerCase().trim();

	// Handle special mapping cases based on field type and properties
	if (normalizedType === "boolean") {
		// Map boolean fields to RADIO with Yes/No options
		return "RADIO";
	}

	if (normalizedType === "select" && ccField.allowMultipleValues === true) {
		return "MULTIPLE_OPTIONS";
	}

	// Use standard mapping for other cases
	return (
		CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] ||
		CC_TO_AP_DATA_TYPE_MAPPING.default
	);
}

/**
 * Check if a CC custom field should be mapped to an AP standard field
 * Returns the AP standard field name if mapping exists, null otherwise
 */
function getStandardFieldMapping(
	ccFieldName: string,
	ccFieldLabel: string,
): string | null {
	// Check both field name and label against the mapping using exact matching
	for (const [ccFieldPattern, apStandardField] of Object.entries(
		CC_TO_AP_STANDARD_FIELD_MAPPING,
	)) {
		if (
			ccFieldPattern === ccFieldName ||
			ccFieldPattern === ccFieldLabel ||
			ccFieldPattern.toLowerCase() === ccFieldName.toLowerCase() ||
			ccFieldPattern.toLowerCase() === ccFieldLabel.toLowerCase()
		) {
			return apStandardField as string;
		}
	}

	return null;
}

/**
 * Extract standard field mappings from CC custom fields
 * Returns a map of AP standard field names to their values
 */
function extractStandardFieldMappings(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Record<string, string> {
	const standardMappings: Record<string, string> = {};

	logInfo(
		requestId,
		`Extracting standard field mappings from ${ccPatientCustomFields.length} CC custom fields`,
	);

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if this CC custom field should map to an AP standard field
		const apStandardField = getStandardFieldMapping(fieldName, fieldLabel);

		if (apStandardField) {
			// Extract field value
			const fieldValue = extractFieldValues(ccCustomField);

			if (fieldValue && fieldValue.trim() !== "") {
				// Handle multiple mappings to the same standard field - prioritize non-empty values
				if (
					!standardMappings[apStandardField] ||
					standardMappings[apStandardField].trim() === ""
				) {
					standardMappings[apStandardField] = fieldValue.trim();
					logDebug(
						requestId,
						`Standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} = "${fieldValue.substring(
							0,
							50,
						)}${fieldValue.length > 50 ? "..." : ""}"`,
					);
				} else {
					logDebug(
						requestId,
						`Skipping duplicate standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} (already mapped)`,
					);
				}
			} else {
				logDebug(
					requestId,
					`Skipping standard field mapping for empty value: ${fieldName} (${fieldLabel})`,
				);
			}
		}
	}

	logInfo(
		requestId,
		`Extracted ${
			Object.keys(standardMappings).length
		} standard field mappings`,
	);
	return standardMappings;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 *
 * Processes CliniCore custom field allowed values and converts them to the format
 * expected by AutoPatient custom fields. Ensures the current field value is included
 * as an option even if not in the original allowed values list.
 *
 * @param ccField - CC custom field with allowedValues array
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @param requestId - Request ID for logging correlation
 * @returns Array of option strings for AP custom field creation
 *
 * @example
 * ```typescript
* const options = extractTextBoxListOptions(
 *   { allowedValues: [{value: "Option1"}, {value: "Option2"}] },
 *   "Option3",
 *   "req-123"
 * );
 * // Returns: ["Option1", "Option2", "Option3"]
 *
```
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
	requestId: string,
): string[] {
	const options: string[] = [];

	logDebug(
		requestId,
		`Extracting options for field ${ccField.label} (type: ${ccField.type})`,
	);
	logDebug(
		requestId,
		`CC allowedValues: ${JSON.stringify(ccField.allowedValues, null, 2)}`,
	);

	// Extract allowed values from CC field
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			if (allowedValue.value && allowedValue.value.trim() !== "") {
				const trimmedValue = allowedValue.value.trim();
				options.push(trimmedValue);
				logDebug(requestId, `Added option: "${trimmedValue}"`);
			}
		}
	}

	// Ensure current value is included as an option if it's not already present
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();
		const existingOption = options.find(
			(option) => option.toLowerCase() === currentValueTrimmed.toLowerCase(),
		);

		if (!existingOption) {
			options.push(currentValueTrimmed);
			logDebug(
				requestId,
				`Added current value as option: "${currentValueTrimmed}"`,
			);
		}
	}

	// If no options were found, create a default option based on current value
	if (options.length === 0 && currentValue && currentValue.trim() !== "") {
		const trimmedValue = currentValue.trim();
		options.push(trimmedValue);
		logDebug(requestId, `Created default option: "${trimmedValue}"`);
	}

	return options;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	logProcessingStep(
		requestId,
		`Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		logInfo(
			requestId,
			`No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		logInfo(
			requestId,
			`Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(requestId, `No custom field data returned from CC`);
			return;
		}

		// Step 3: Extract and apply standard field mappings
		const standardFieldMappings = extractStandardFieldMappings(
			ccPatientCustomFields,
			requestId,
		);

		// Build update payload for standard fields
		const standardFieldUpdate: Partial<PostAPContactType> = {};

		// Apply standard field mappings to AP contact if any exist
		if (Object.keys(standardFieldMappings).length > 0) {
			logInfo(
				requestId,
				`Applying ${
					Object.keys(standardFieldMappings).length
				} standard field mappings to AP contact`,
			);

			for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
				if (fieldName === "phone") {
					standardFieldUpdate.phone = value;
				} else if (fieldName === "email") {
					standardFieldUpdate.email = value;
				}
				// Add more standard field mappings as needed
			}
		}

		// Step 4: Filter out excluded fields and extract valid custom field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			logInfo(
				requestId,
				`No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping (with validation)
		logInfo(requestId, `Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Validate that API response contains no duplicate fieldKeys
		validateNoDuplicateFieldKeys(apCustomFields, requestId);
		logDebug(requestId, `Validation passed: No duplicate fieldKeys found in ${apCustomFields.length} AP custom fields`);

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			logInfo(requestId, `No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		logInfo(
			requestId,
			`Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		let updatePayload: Partial<PostAPContactType> = {
			customFields: apCustomFieldMappings,
		};
		if (
			Object.keys(standardFieldMappings).length > 0 &&
			Object.keys(standardFieldUpdate).length > 0
		) {
			updatePayload = {
				...updatePayload,
				...standardFieldUpdate,
			};
		}
		await contactReq.update(apContactId, updatePayload);

		logInfo(requestId, `Custom field sync completed successfully`);
	} catch (error) {
		logError(requestId, `Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (
			isStandardContactField(fieldName) ||
			isStandardContactField(fieldLabel)
		) {
			logDebug(
				requestId,
				`Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Check if field should be mapped to an AP standard field instead of custom field
		const standardFieldMapping = getStandardFieldMapping(fieldName, fieldLabel);
		if (standardFieldMapping) {
			logDebug(
				requestId,
				`Excluding field mapped to standard field: ${fieldName} (${fieldLabel}) -> AP.${standardFieldMapping}`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			logDebug(
				requestId,
				`Skipping field with empty value: ${fieldName}`,
			);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		logDebug(
			requestId,
			`Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(
				0,
				100,
			)}${fieldValue.length > 100 ? "..." : ""}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];
	let createdCount = 0;
	let existingCount = 0;

	logInfo(
		requestId,
		`Starting custom field mapping for ${validCustomFields.length} CC fields against ${apCustomFields.length} existing AP fields`,
	);

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using enhanced matching with API fallback
			let apCustomField = await findExistingApFieldWithApiCheck(
				apCustomFields,
				field,
				requestId,
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				const mappedDataType = mapCcToApDataType(field);
				const fieldKey = generateApFieldKey(field.name);

				// Simple existence check using exact matching
				const existingField = apCustomFields.find(f =>
					f.fieldKey === fieldKey ||
					f.fieldKey === `contact.${fieldKey}`
				);

				if (existingField) {
					logError(
						requestId,
						`FIELD MATCHING ERROR: Found existing field "${existingField.name}" (ID: ${existingField.id}, fieldKey: "${existingField.fieldKey}") that should have been detected by exact matching logic`
					);
					// Use the existing field
					apCustomField = existingField;
					existingCount++;
				} else {
					logInfo(
						requestId,
						`Creating new AP custom field: "${field.label}" with fieldKey: "${fieldKey}" (type: ${field.type}${field.allowMultipleValues ? ' (multiple)' : ''} -> ${mappedDataType}${field.type.toLowerCase() === 'boolean' ? ' with Yes/No options' : ''})`,
					);

					const createData: APPostCustomfieldType = {
					name: field.label, // Use CC label as AP name (user-friendly)
					dataType: mappedDataType,
					model: "contact", // Set model to contact as required
					fieldKey: fieldKey, // Use CC name for fieldKey (normalized)
				};

				// Add conditional properties based on data type
				if (mappedDataType === "RADIO" && field.type.toLowerCase() === "boolean") {
					// For boolean fields mapped to RADIO, add Yes/No options
					createData.options = ["Yes", "No"];

					logDebug(
						requestId,
						`Adding Yes/No options for boolean field: ${field.label}`,
					);
				} else if (
					mappedDataType === "SINGLE_OPTIONS" ||
					mappedDataType === "MULTIPLE_OPTIONS"
				) {
					// Extract allowed values from CC field and convert to AP format
					const textBoxListOptions = extractTextBoxListOptions(field, value, requestId);

					if (textBoxListOptions.length > 0) {
						// Based on successful test: API expects "options" as simple string array
						createData.options = textBoxListOptions;

						logDebug(
							requestId,
							`Adding ${textBoxListOptions.length} options to ${
								field.label
							}: ${textBoxListOptions.join(", ")}`,
						);
						logDebug(
							requestId,
							`Using simple string array for options (confirmed working structure)`,
						);
					} else {
						// If no options can be created, fall back to TEXT type to avoid API error
						logDebug(
							requestId,
							`No options available for ${field.label}, falling back to TEXT type`,
						);
						createData.dataType = "TEXT";
					}
				}

				try {
					apCustomField = await apCustomfield.create(createData);

					// Add to our local cache to avoid duplicate creation
					apCustomFields.push(apCustomField);

					logInfo(
						requestId,
						`Successfully created AP custom field: "${field.label}" with ID: ${apCustomField.id}`,
					);
					createdCount++;
				} catch (createError: unknown) {
					const errorMessage =
						createError instanceof Error
							? createError.message
							: String(createError);

					// Handle duplicate fieldKey error by trying to find the existing field
					if (errorMessage.includes("already exists")) {
						logInfo(
							requestId,
							`Field creation failed - already exists. Error: "${errorMessage}"`,
						);
						logDebug(
							requestId,
							`Attempting to find existing field for CC field "${field.name}" with generated fieldKey "${fieldKey}"`,
						);

						// Strategy 1: Try enhanced field finding with fresh API data
						apCustomField = await findExistingApFieldWithApiCheck(
							apCustomFields,
							field,
							requestId,
						);

						// Strategy 2: If not found, try extracting fieldKey from error message
						if (!apCustomField) {
							const extractedFieldKey = extractFieldKeyFromError(errorMessage);
							if (extractedFieldKey) {
								logDebug(
									requestId,
									`Extracted fieldKey "${extractedFieldKey}" from error message, searching for field`,
								);

								// Refresh fields and search by extracted fieldKey
								try {
									const freshFields = await apCustomfield.all();
									apCustomField = findFieldByExtractedKey(freshFields, extractedFieldKey, requestId);

									if (apCustomField) {
										// Update cache with fresh data
										apCustomFields.length = 0;
										apCustomFields.push(...freshFields);
									}
								} catch (refreshError) {
									logError(requestId, `Failed to refresh fields for extracted fieldKey search:`, refreshError);
								}
							}
						}

						if (apCustomField) {
							logInfo(
								requestId,
								`Successfully found existing AP custom field: "${apCustomField.name}" (ID: ${apCustomField.id}, fieldKey: "${apCustomField.fieldKey}")`,
							);
							existingCount++;
						} else {
							logError(
								requestId,
								`Could not find existing field after duplicate error. CC field: "${field.name}", generated fieldKey: "${fieldKey}", error: "${errorMessage}"`,
							);
							throw createError;
						}
					} else {
						throw createError;
					}
				}
				} // Close the else block for field creation
			} else {
				logDebug(
					requestId,
					`Using existing AP custom field: "${apCustomField.name}" with ID: ${apCustomField.id}`,
				);
				existingCount++;
			}

			// Transform value for boolean fields
			const transformedValue = transformBooleanValue(value, field.type);

			mappings.push({
				id: apCustomField.id,
				value: transformedValue,
			});

			logDebug(
				requestId,
				`Mapped: "${field.label}" -> AP Field ID ${
					apCustomField.id
				} (${transformedValue.substring(0, 50)}${transformedValue.length > 50 ? "..." : ""})${
					transformedValue !== value ? ` [transformed from: ${value}]` : ""
				}`,
			);
		} catch (error) {
			logError(
				requestId,
				`Failed to map field "${field.name}":`,
				error,
			);
			// Continue with other fields rather than failing completely
		}
	}

	logInfo(
		requestId,
		`Custom field mapping completed: ${mappings.length} total mappings (${createdCount} created, ${existingCount} existing)`,
	);

	return mappings;
}
